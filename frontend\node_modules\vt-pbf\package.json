{"name": "vt-pbf", "version": "3.1.3", "description": "Serialize mapbox vector tiles to binary protobufs in javascript.", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"pretest": "eslint index.js test/*.js", "test": "tape test/*.js"}, "author": "<PERSON> <<EMAIL>> (http://anandthakker.net/)", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mapbox/vt-pbf.git"}, "dependencies": {"@mapbox/point-geometry": "0.1.0", "@mapbox/vector-tile": "^1.3.1", "pbf": "^3.2.1"}, "devDependencies": {"@mapbox/geojson-fixtures": "^1.0.0", "@mapbox/mvt-fixtures": "^3.6.0", "@mapbox/vtvalidate": "0.2.3", "benchmark": "^2.1.4", "eslint": "^7.26.0", "eslint-config-standard": "^16.0.2", "eslint-plugin-import": "^2.23.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-standard": "^4.1.0", "geojson-equality": "^0.2.0", "geojson-vt": "^3.2.1", "standard": "^16.0.3", "tape": "^5.2.2"}}