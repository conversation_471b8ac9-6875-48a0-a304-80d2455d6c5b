{"name": "umzug", "version": "2.3.0", "description": "Framework agnostic migration tool for Node.JS", "keywords": ["migrate", "migration", "migrations", "sequelize"], "main": "lib/index.js", "dependencies": {"bluebird": "^3.7.2"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/plugin-proposal-object-rest-spread": "^7.9.0", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.0", "@babel/register": "^7.9.0", "babel-plugin-dynamic-import-node": "^2.3.0", "babel-plugin-syntax-dynamic-import": "^6.18.0", "chai": "^4.2.0", "coffee-script": "^1.8.0", "eslint": "^5.9.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-mocha": "^6.3.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "lodash": "^4.17.15", "mocha": "^6.2.2", "sequelize": "^5.21.5", "sinon": "^7.5.0", "sinon-chai": "^3.5.0", "sqlite3": "^4.1.1", "typescript": "^3.8.3", "uuid": "^7.0.2"}, "scripts": {"lint": "eslint src test", "prepare": "babel src --out-dir lib", "test": "mocha -r @babel/register --check-leaks test/index.js"}, "repository": {"type": "git", "url": "https://github.com/sequelize/umzug.git"}, "author": "Sascha <PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/sequelize/umzug/issues"}, "homepage": "https://github.com/sequelize/umzug", "engines": {"node": ">=6.0.0"}}