interactive-learning-platform/
│
├── 📁 backend/
│   ├── 📁 src/
│   │   ├── 📁 config/
│   │   │   ├── database.js
│   │   │   └── config.js
│   │   ├── 📁 controllers/
│   │   │   ├── authController.js
│   │   │   ├── subjectController.js
│   │   │   ├── lessonController.js
│   │   │   ├── exerciseController.js
│   │   │   ├── examController.js
│   │   │   ├── analyticsController.js
│   │   │   └── simulationController.js
│   │   ├── 📁 middleware/
│   │   │   ├── auth.js
│   │   │   ├── validation.js
│   │   │   └── errorHandler.js
│   │   ├── 📁 models/
│   │   │   ├── User.js
│   │   │   ├── Subject.js
│   │   │   ├── Lesson.js
│   │   │   ├── Exercise.js
│   │   │   ├── Exam.js
│   │   │   ├── Progress.js
│   │   │   └── index.js
│   │   ├── 📁 routes/
│   │   │   ├── auth.js
│   │   │   ├── subjects.js
│   │   │   ├── lessons.js
│   │   │   ├── exercises.js
│   │   │   ├── exams.js
│   │   │   ├── analytics.js
│   │   │   └── index.js
│   │   ├── 📁 services/
│   │   │   ├── authService.js
│   │   │   ├── emailService.js
│   │   │   └── analyticsService.js
│   │   ├── 📁 utils/
│   │   │   ├── helpers.js
│   │   │   ├── constants.js
│   │   │   └── validators.js
│   │   └── app.js
│   ├── 📁 migrations/
│   ├── 📁 seeders/
│   ├── package.json
│   └── server.js
│
├── 📁 frontend/
│   ├── 📁 public/
│   │   ├── index.html
│   │   └── manifest.json
│   ├── 📁 src/
│   │   ├── 📁 components/
│   │   │   ├── 📁 common/
│   │   │   │   ├── Header.jsx
│   │   │   │   ├── Sidebar.jsx
│   │   │   │   ├── LoadingSpinner.jsx
│   │   │   │   ├── Modal.jsx
│   │   │   │   └── ProgressBar.jsx
│   │   │   ├── 📁 dashboard/
│   │   │   │   ├── Dashboard.jsx
│   │   │   │   ├── StatsCard.jsx
│   │   │   │   ├── RecentActivities.jsx
│   │   │   │   └── StudySchedule.jsx
│   │   │   ├── 📁 subjects/
│   │   │   │   ├── SubjectList.jsx
│   │   │   │   ├── SubjectCard.jsx
│   │   │   │   └── LessonItem.jsx
│   │   │   ├── 📁 lessons/
│   │   │   │   ├── LessonViewer.jsx
│   │   │   │   ├── LessonContent.jsx
│   │   │   │   └── LessonProgress.jsx
│   │   │   ├── 📁 exercises/
│   │   │   │   ├── ExerciseList.jsx
│   │   │   │   ├── ExerciseCard.jsx
│   │   │   │   ├── ExerciseViewer.jsx
│   │   │   │   └── ExerciseSolution.jsx
│   │   │   ├── 📁 exams/
│   │   │   │   ├── ExamList.jsx
│   │   │   │   ├── ExamViewer.jsx
│   │   │   │   ├── QuestionCard.jsx
│   │   │   │   └── ExamResults.jsx
│   │   │   ├── 📁 simulations/
│   │   │   │   ├── SimulationList.jsx
│   │   │   │   ├── FunctionPlotter.jsx
│   │   │   │   ├── PhysicsLab.jsx
│   │   │   │   └── BiologyModels.jsx
│   │   │   └── 📁 analytics/
│   │   │       ├── Analytics.jsx
│   │   │       ├── PerformanceChart.jsx
│   │   │       ├── SubjectProgressChart.jsx
│   │   │       └── StudyTimeChart.jsx
│   │   ├── 📁 hooks/
│   │   │   ├── useAuth.js
│   │   │   ├── useApi.js
│   │   │   ├── useLocalStorage.js
│   │   │   └── useTimer.js
│   │   ├── 📁 context/
│   │   │   ├── AuthContext.jsx
│   │   │   ├── ThemeContext.jsx
│   │   │   └── AppContext.jsx
│   │   ├── 📁 services/
│   │   │   ├── api.js
│   │   │   ├── authService.js
│   │   │   └── localStorageService.js
│   │   ├── 📁 utils/
│   │   │   ├── helpers.js
│   │   │   ├── constants.js
│   │   │   └── formatters.js
│   │   ├── 📁 styles/
│   │   │   ├── index.css
│   │   │   ├── components.css
│   │   │   └── animations.css
│   │   ├── App.jsx
│   │   └── index.js
│   ├── package.json
│   └── tailwind.config.js
│
├── 📁 database/
│   ├── 📁 migrations/
│   ├── 📁 seeds/
│   └── schema.sql
│
├── .gitignore
├── README.md
└── docker-compose.yml