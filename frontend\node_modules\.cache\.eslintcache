[{"D:\\2025\\baccc\\frontend\\src\\index.js": "1", "D:\\2025\\baccc\\frontend\\src\\App.jsx": "2", "D:\\2025\\baccc\\frontend\\src\\hooks\\useAuth.js": "3", "D:\\2025\\baccc\\frontend\\src\\context\\AppContext.jsx": "4", "D:\\2025\\baccc\\frontend\\src\\context\\AuthContext.jsx": "5", "D:\\2025\\baccc\\frontend\\src\\context\\ThemeContext.jsx": "6", "D:\\2025\\baccc\\frontend\\src\\components\\dashboard\\Dashboard.jsx": "7", "D:\\2025\\baccc\\frontend\\src\\components\\simulations\\SimulationList.jsx": "8", "D:\\2025\\baccc\\frontend\\src\\components\\lessons\\LessonViewer.jsx": "9", "D:\\2025\\baccc\\frontend\\src\\components\\exercises\\ExerciseList.jsx": "10", "D:\\2025\\baccc\\frontend\\src\\components\\subjects\\SubjectList.jsx": "11", "D:\\2025\\baccc\\frontend\\src\\components\\analytics\\Analytics.jsx": "12", "D:\\2025\\baccc\\frontend\\src\\components\\exams\\ExamList.jsx": "13", "D:\\2025\\baccc\\frontend\\src\\components\\common\\Sidebar.jsx": "14", "D:\\2025\\baccc\\frontend\\src\\components\\common\\Header.jsx": "15", "D:\\2025\\baccc\\frontend\\src\\services\\authService.js": "16", "D:\\2025\\baccc\\frontend\\src\\services\\api.js": "17"}, {"size": 260, "mtime": 1754337080000, "results": "18", "hashOfConfig": "19"}, {"size": 3098, "mtime": 1754337105174, "results": "20", "hashOfConfig": "19"}, {"size": 118, "mtime": 1754337194054, "results": "21", "hashOfConfig": "19"}, {"size": 1579, "mtime": 1754337157976, "results": "22", "hashOfConfig": "19"}, {"size": 3317, "mtime": 1754337129621, "results": "23", "hashOfConfig": "19"}, {"size": 895, "mtime": 1754337141849, "results": "24", "hashOfConfig": "19"}, {"size": 3577, "mtime": 1754337285737, "results": "25", "hashOfConfig": "19"}, {"size": 445, "mtime": 1754337352659, "results": "26", "hashOfConfig": "19"}, {"size": 436, "mtime": 1754337317421, "results": "27", "hashOfConfig": "19"}, {"size": 437, "mtime": 1754337330533, "results": "28", "hashOfConfig": "19"}, {"size": 2432, "mtime": 1754337304780, "results": "29", "hashOfConfig": "19"}, {"size": 439, "mtime": 1754337364252, "results": "30", "hashOfConfig": "19"}, {"size": 425, "mtime": 1754337342003, "results": "31", "hashOfConfig": "19"}, {"size": 1990, "mtime": 1754337263439, "results": "32", "hashOfConfig": "19"}, {"size": 1677, "mtime": 1754337245643, "results": "33", "hashOfConfig": "19"}, {"size": 903, "mtime": 1754337184316, "results": "34", "hashOfConfig": "19"}, {"size": 968, "mtime": 1754337171766, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kz6ad4", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\2025\\baccc\\frontend\\src\\index.js", [], [], "D:\\2025\\baccc\\frontend\\src\\App.jsx", [], [], "D:\\2025\\baccc\\frontend\\src\\hooks\\useAuth.js", [], [], "D:\\2025\\baccc\\frontend\\src\\context\\AppContext.jsx", [], [], "D:\\2025\\baccc\\frontend\\src\\context\\AuthContext.jsx", [], [], "D:\\2025\\baccc\\frontend\\src\\context\\ThemeContext.jsx", [], [], "D:\\2025\\baccc\\frontend\\src\\components\\dashboard\\Dashboard.jsx", [], [], "D:\\2025\\baccc\\frontend\\src\\components\\simulations\\SimulationList.jsx", [], [], "D:\\2025\\baccc\\frontend\\src\\components\\lessons\\LessonViewer.jsx", [], [], "D:\\2025\\baccc\\frontend\\src\\components\\exercises\\ExerciseList.jsx", [], [], "D:\\2025\\baccc\\frontend\\src\\components\\subjects\\SubjectList.jsx", [], [], "D:\\2025\\baccc\\frontend\\src\\components\\analytics\\Analytics.jsx", [], [], "D:\\2025\\baccc\\frontend\\src\\components\\exams\\ExamList.jsx", [], [], "D:\\2025\\baccc\\frontend\\src\\components\\common\\Sidebar.jsx", [], [], "D:\\2025\\baccc\\frontend\\src\\components\\common\\Header.jsx", [], [], "D:\\2025\\baccc\\frontend\\src\\services\\authService.js", ["87"], [], "D:\\2025\\baccc\\frontend\\src\\services\\api.js", [], [], {"ruleId": "88", "severity": 1, "message": "89", "line": 38, "column": 1, "nodeType": "90", "endLine": 38, "endColumn": 34}, "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration"]