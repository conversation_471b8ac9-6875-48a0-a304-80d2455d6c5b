{"name": "learning-platform-backend", "version": "1.0.0", "description": "Interactive Learning Platform Backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "migrate": "npx sequelize-cli db:migrate", "seed": "npx sequelize-cli db:seed:all", "test": "jest --watchAll=false", "test:watch": "jest --watch"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "express-rate-limit": "^6.11.2", "express-validator": "^6.15.0", "helmet": "^6.2.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.1", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7", "socket.io": "^4.8.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^2.0.22", "sequelize-cli": "^6.6.3", "supertest": "^6.3.4"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs"}