[ignore]
.*\.svg
.*\.png
.*/\.nyc_output/.*
.*/docs/.*
.*/node_modules/.cache/.*
.*/node_modules/.*/tests?/.*
.*/node_modules/@mapbox/jsonlint-lines-primitives/.*
.*/node_modules/@mapbox/mvt-fixtures/.*
.*/node_modules/@mapbox/geojson-types/fixtures/.*
.*/node_modules/@mapbox/mr-ui/.*
.*/node_modules/@mapbox/dr-ui/.*
.*/node_modules/@mapbox/batfish/.*
.*/node_modules/browserify/.*
.*/node_modules/browser-sync.*/.*
.*/node_modules/nyc/.*
.*/node_modules/fbjs/.*
.*/node_modules/es5-ext/.*
.*/node_modules/jsdom/.*
.*/node_modules/eslint.*/.*
.*/node_modules/highlight.*/.*
.*/node_modules/rxjs/.*
.*/node_modules/@?babel.*/.*
.*/node_modules/react.*/.*
.*/node_modules/svgo/.*
.*/node_modules/moment/.*
.*/node_modules/regenerate-unicode-properties/.*
.*/node_modules/remark.*/.*
.*/node_modules/webpack/.*
.*/node_modules/caniuse-lite/.*
.*/node_modules/d3.*/.*
.*/node_modules/css-tree/.*
.*/node_modules/lodash/.*
.*/node_modules/fsevents/.*
.*/node_modules/browser-sync-client/.*
.*/node_modules/core-js.*/.*
.*/node_modules/stylelint/.*
.*/node_modules/postcss.*/.*
.*/node_modules/prismjs.*/.*
.*/node_modules/documentation/.*
.*/node_modules/module-deps/.*
.*/test/unit/style-spec/fixture/invalidjson.input.json
.*/render-tests/.*
.*/query-tests/.*
.*/expression-tests/.*
.*/test/build/downstream-flow-fixture/.*
.*/_batfish_tmp/.*
.*/_site/.*

[version]
0.100.0

[options]
server.max_workers=4

[strict]
nonstrict-import
unclear-type
untyped-import
untyped-type-import
sketchy-null
