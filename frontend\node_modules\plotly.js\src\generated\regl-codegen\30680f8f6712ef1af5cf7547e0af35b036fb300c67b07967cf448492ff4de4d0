module.exports = function anonymous(g0,g48,g114,g124,g126,g132,g134,g194
) {
"use strict";
var v1,v2,v3,v4,v5,v6,v7,v8,v9,v10,v11,v12,v13,v14,v15,v16,v17,v18,v19,v20,v21,v22,v23,v24,v25,v26,v27,v28,v29,v30,v31,v32,v33,v34,v35,v36,v37,v38,v39,v40,v41,v42,v43,v44,v45,v46,v47,v111,v191;
v1=g0.gl;
v2=g0.context;
v3=g0.strings;
v4=g0.next;
v5=g0.current;
v6=g0.draw;
v7=g0.elements;
v8=g0.buffer;
v9=g0.shader;
v10=g0.attributes;
v11=g0.vao;
v12=g0.uniforms;
v13=g0.framebuffer;
v14=g0.extensions;
v15=g0.timer;
v16=g0.isBufferArgs;
v17=v4.blend_color;
v18=v5.blend_color;
v19=v4.blend_equation;
v20=v5.blend_equation;
v21=v4.blend_func;
v22=v5.blend_func;
v23=v4.depth_range;
v24=v5.depth_range;
v25=v4.colorMask;
v26=v5.colorMask;
v27=v4.polygonOffset_offset;
v28=v5.polygonOffset_offset;
v29=v4.sample_coverage;
v30=v5.sample_coverage;
v31=v4.stencil_func;
v32=v5.stencil_func;
v33=v4.stencil_opFront;
v34=v5.stencil_opFront;
v35=v4.stencil_opBack;
v36=v5.stencil_opBack;
v37=v4.scissor_box;
v38=v5.scissor_box;
v39=v4.viewport;
v40=v5.viewport;
v41={
"points":0,"point":0,"lines":1,"line":1,"triangles":4,"triangle":4,"line loop":2,"line strip":3,"triangle strip":5,"triangle fan":6}
;
v42={
"never":512,"less":513,"<":513,"equal":514,"=":514,"==":514,"===":514,"lequal":515,"<=":515,"greater":516,">":516,"notequal":517,"!=":517,"!==":517,"gequal":518,">=":518,"always":519}
;
v43={
"0":0,"1":1,"zero":0,"one":1,"src color":768,"one minus src color":769,"src alpha":770,"one minus src alpha":771,"dst color":774,"one minus dst color":775,"dst alpha":772,"one minus dst alpha":773,"constant color":32769,"one minus constant color":32770,"constant alpha":32771,"one minus constant alpha":32772,"src alpha saturate":776}
;
v44={
"add":32774,"subtract":32778,"reverse subtract":32779}
;
v45={
"0":0,"zero":0,"keep":7680,"replace":7681,"increment":7682,"decrement":7683,"increment wrap":34055,"decrement wrap":34056,"invert":5386}
;
v46={
"int8":5120,"int16":5122,"int32":5124,"uint8":5121,"uint16":5123,"uint32":5125,"float":5126,"float32":5126}
;
v47={
"cw":2304,"ccw":2305}
;
v111={
}
;
v191={
}
;
return {
"draw":function(a0){
var v49,v50,v51,v52,v53,v100,v101,v102,v103,v104,v105,v106,v107,v108,v109,v110,v112,v113;
v49=a0["framebuffer"];
v50=v13.getFramebuffer(v49);
v51=v13.next;
v13.next=v50;
v52=v2.framebufferWidth;
v2.framebufferWidth=v50?v50.width:v2.drawingBufferWidth;
v53=v2.framebufferHeight;
v2.framebufferHeight=v50?v50.height:v2.drawingBufferHeight;
if(v50!==v13.cur){
if(v50){
v1.bindFramebuffer(36160,v50.framebuffer);
}
else{
v1.bindFramebuffer(36160,null);
}
v13.cur=v50;
}
if(v5.dirty){
var v54,v55,v56,v57,v58,v59,v60,v61,v62,v63,v64,v65,v66,v67,v68,v69,v70,v71,v72,v73,v74,v75,v76,v77,v78,v79,v80,v81,v82,v83,v84,v85,v86,v87,v88,v89,v90,v91,v92,v93,v94,v95,v96,v97,v98,v99;
v54=v4.dither;
if(v54!==v5.dither){
if(v54){
v1.enable(3024);
}
else{
v1.disable(3024);
}
v5.dither=v54;
}
v55=v4.blend_enable;
if(v55!==v5.blend_enable){
if(v55){
v1.enable(3042);
}
else{
v1.disable(3042);
}
v5.blend_enable=v55;
}
v56=v17[0];
v57=v17[1];
v58=v17[2];
v59=v17[3];
if(v56!==v18[0]||v57!==v18[1]||v58!==v18[2]||v59!==v18[3]){
v1.blendColor(v56,v57,v58,v59);
v18[0]=v56;
v18[1]=v57;
v18[2]=v58;
v18[3]=v59;
}
v60=v19[0];
v61=v19[1];
if(v60!==v20[0]||v61!==v20[1]){
v1.blendEquationSeparate(v60,v61);
v20[0]=v60;
v20[1]=v61;
}
v62=v21[0];
v63=v21[1];
v64=v21[2];
v65=v21[3];
if(v62!==v22[0]||v63!==v22[1]||v64!==v22[2]||v65!==v22[3]){
v1.blendFuncSeparate(v62,v63,v64,v65);
v22[0]=v62;
v22[1]=v63;
v22[2]=v64;
v22[3]=v65;
}
v66=v4.depth_enable;
if(v66!==v5.depth_enable){
if(v66){
v1.enable(2929);
}
else{
v1.disable(2929);
}
v5.depth_enable=v66;
}
v67=v4.depth_func;
if(v67!==v5.depth_func){
v1.depthFunc(v67);
v5.depth_func=v67;
}
v68=v23[0];
v69=v23[1];
if(v68!==v24[0]||v69!==v24[1]){
v1.depthRange(v68,v69);
v24[0]=v68;
v24[1]=v69;
}
v70=v4.depth_mask;
if(v70!==v5.depth_mask){
v1.depthMask(v70);
v5.depth_mask=v70;
}
v71=v25[0];
v72=v25[1];
v73=v25[2];
v74=v25[3];
if(v71!==v26[0]||v72!==v26[1]||v73!==v26[2]||v74!==v26[3]){
v1.colorMask(v71,v72,v73,v74);
v26[0]=v71;
v26[1]=v72;
v26[2]=v73;
v26[3]=v74;
}
v75=v4.cull_enable;
if(v75!==v5.cull_enable){
if(v75){
v1.enable(2884);
}
else{
v1.disable(2884);
}
v5.cull_enable=v75;
}
v76=v4.cull_face;
if(v76!==v5.cull_face){
v1.cullFace(v76);
v5.cull_face=v76;
}
v77=v4.frontFace;
if(v77!==v5.frontFace){
v1.frontFace(v77);
v5.frontFace=v77;
}
v78=v4.lineWidth;
if(v78!==v5.lineWidth){
v1.lineWidth(v78);
v5.lineWidth=v78;
}
v79=v4.polygonOffset_enable;
if(v79!==v5.polygonOffset_enable){
if(v79){
v1.enable(32823);
}
else{
v1.disable(32823);
}
v5.polygonOffset_enable=v79;
}
v80=v27[0];
v81=v27[1];
if(v80!==v28[0]||v81!==v28[1]){
v1.polygonOffset(v80,v81);
v28[0]=v80;
v28[1]=v81;
}
v82=v4.sample_alpha;
if(v82!==v5.sample_alpha){
if(v82){
v1.enable(32926);
}
else{
v1.disable(32926);
}
v5.sample_alpha=v82;
}
v83=v4.sample_enable;
if(v83!==v5.sample_enable){
if(v83){
v1.enable(32928);
}
else{
v1.disable(32928);
}
v5.sample_enable=v83;
}
v84=v29[0];
v85=v29[1];
if(v84!==v30[0]||v85!==v30[1]){
v1.sampleCoverage(v84,v85);
v30[0]=v84;
v30[1]=v85;
}
v86=v4.stencil_enable;
if(v86!==v5.stencil_enable){
if(v86){
v1.enable(2960);
}
else{
v1.disable(2960);
}
v5.stencil_enable=v86;
}
v87=v4.stencil_mask;
if(v87!==v5.stencil_mask){
v1.stencilMask(v87);
v5.stencil_mask=v87;
}
v88=v31[0];
v89=v31[1];
v90=v31[2];
if(v88!==v32[0]||v89!==v32[1]||v90!==v32[2]){
v1.stencilFunc(v88,v89,v90);
v32[0]=v88;
v32[1]=v89;
v32[2]=v90;
}
v91=v33[0];
v92=v33[1];
v93=v33[2];
v94=v33[3];
if(v91!==v34[0]||v92!==v34[1]||v93!==v34[2]||v94!==v34[3]){
v1.stencilOpSeparate(v91,v92,v93,v94);
v34[0]=v91;
v34[1]=v92;
v34[2]=v93;
v34[3]=v94;
}
v95=v35[0];
v96=v35[1];
v97=v35[2];
v98=v35[3];
if(v95!==v36[0]||v96!==v36[1]||v97!==v36[2]||v98!==v36[3]){
v1.stencilOpSeparate(v95,v96,v97,v98);
v36[0]=v95;
v36[1]=v96;
v36[2]=v97;
v36[3]=v98;
}
v99=v4.scissor_enable;
if(v99!==v5.scissor_enable){
if(v99){
v1.enable(3089);
}
else{
v1.disable(3089);
}
v5.scissor_enable=v99;
}
}
v100=v2.framebufferWidth;
v101=v2.framebufferHeight;
v102=v2.viewportWidth;
v2.viewportWidth=v100;
v103=v2.viewportHeight;
v2.viewportHeight=v101;
v1.viewport(0,0,v100,v101);
v40[0]=0;
v40[1]=0;
v40[2]=v100;
v40[3]=v101;
v104=v2.framebufferWidth;
v105=v2.framebufferHeight;
v1.scissor(0,0,v104,v105);
v38[0]=0;
v38[1]=0;
v38[2]=v104;
v38[3]=v105;
v106=v5.profile;
if(v106){
v107=performance.now();
g48.count++;
}
v108=v9.frag;
v109=v9.vert;
v110=v9.program(v109,v108);
v1.useProgram(v110.program);
v11.setVAO(null);
v112=v110.id;
v113=v111[v112];
if(v113){
v113.call(this,a0);
}
else{
v113=v111[v112]=g114(v110);
v113.call(this,a0);
}
v5.dirty=true;
v11.setVAO(null);
v13.next=v51;
v2.framebufferWidth=v52;
v2.framebufferHeight=v53;
v2.viewportWidth=v102;
v2.viewportHeight=v103;
if(v106){
g48.cpuTime+=performance.now()-v107;
}
}
,"scope":function(a0,a1,a2){
var v115,v116,v117,v118,v119,v120,v121,v122,v123,v125,v127,v128,v129,v130,v131,v133,v135,v136,v137,v138,v139;
v115=a0["framebuffer"];
v116=v13.getFramebuffer(v115);
v117=v13.next;
v13.next=v116;
v118=v2.framebufferWidth;
v2.framebufferWidth=v116?v116.width:v2.drawingBufferWidth;
v119=v2.framebufferHeight;
v2.framebufferHeight=v116?v116.height:v2.drawingBufferHeight;
v120=v2.framebufferWidth;
v121=v2.framebufferHeight;
v122=v2.viewportWidth;
v2.viewportWidth=v120;
v123=v2.viewportHeight;
v2.viewportHeight=v121;
v125=v39[0];
v39[0]=g124;
v127=v39[1];
v39[1]=g126;
v128=v39[2];
v39[2]=v120;
v129=v39[3];
v39[3]=v121;
v130=v2.framebufferWidth;
v131=v2.framebufferHeight;
v133=v37[0];
v37[0]=g132;
v135=v37[1];
v37[1]=g134;
v136=v37[2];
v37[2]=v130;
v137=v37[3];
v37[3]=v131;
v138=v5.profile;
if(v138){
v139=performance.now();
g48.count++;
}
v5.dirty=true;
a1(v2,a0,a2);
v13.next=v117;
v2.framebufferWidth=v118;
v2.framebufferHeight=v119;
v2.viewportWidth=v122;
v2.viewportHeight=v123;
v39[0]=v125;
v39[1]=v127;
v39[2]=v128;
v39[3]=v129;
v37[0]=v133;
v37[1]=v135;
v37[2]=v136;
v37[3]=v137;
if(v138){
g48.cpuTime+=performance.now()-v139;
}
v5.dirty=true;
}
,"batch":function(a0,a1){
var v186,v187,v188,v189,v190,v192,v193;
if(v5.dirty){
var v140,v141,v142,v143,v144,v145,v146,v147,v148,v149,v150,v151,v152,v153,v154,v155,v156,v157,v158,v159,v160,v161,v162,v163,v164,v165,v166,v167,v168,v169,v170,v171,v172,v173,v174,v175,v176,v177,v178,v179,v180,v181,v182,v183,v184,v185;
v140=v4.dither;
if(v140!==v5.dither){
if(v140){
v1.enable(3024);
}
else{
v1.disable(3024);
}
v5.dither=v140;
}
v141=v4.blend_enable;
if(v141!==v5.blend_enable){
if(v141){
v1.enable(3042);
}
else{
v1.disable(3042);
}
v5.blend_enable=v141;
}
v142=v17[0];
v143=v17[1];
v144=v17[2];
v145=v17[3];
if(v142!==v18[0]||v143!==v18[1]||v144!==v18[2]||v145!==v18[3]){
v1.blendColor(v142,v143,v144,v145);
v18[0]=v142;
v18[1]=v143;
v18[2]=v144;
v18[3]=v145;
}
v146=v19[0];
v147=v19[1];
if(v146!==v20[0]||v147!==v20[1]){
v1.blendEquationSeparate(v146,v147);
v20[0]=v146;
v20[1]=v147;
}
v148=v21[0];
v149=v21[1];
v150=v21[2];
v151=v21[3];
if(v148!==v22[0]||v149!==v22[1]||v150!==v22[2]||v151!==v22[3]){
v1.blendFuncSeparate(v148,v149,v150,v151);
v22[0]=v148;
v22[1]=v149;
v22[2]=v150;
v22[3]=v151;
}
v152=v4.depth_enable;
if(v152!==v5.depth_enable){
if(v152){
v1.enable(2929);
}
else{
v1.disable(2929);
}
v5.depth_enable=v152;
}
v153=v4.depth_func;
if(v153!==v5.depth_func){
v1.depthFunc(v153);
v5.depth_func=v153;
}
v154=v23[0];
v155=v23[1];
if(v154!==v24[0]||v155!==v24[1]){
v1.depthRange(v154,v155);
v24[0]=v154;
v24[1]=v155;
}
v156=v4.depth_mask;
if(v156!==v5.depth_mask){
v1.depthMask(v156);
v5.depth_mask=v156;
}
v157=v25[0];
v158=v25[1];
v159=v25[2];
v160=v25[3];
if(v157!==v26[0]||v158!==v26[1]||v159!==v26[2]||v160!==v26[3]){
v1.colorMask(v157,v158,v159,v160);
v26[0]=v157;
v26[1]=v158;
v26[2]=v159;
v26[3]=v160;
}
v161=v4.cull_enable;
if(v161!==v5.cull_enable){
if(v161){
v1.enable(2884);
}
else{
v1.disable(2884);
}
v5.cull_enable=v161;
}
v162=v4.cull_face;
if(v162!==v5.cull_face){
v1.cullFace(v162);
v5.cull_face=v162;
}
v163=v4.frontFace;
if(v163!==v5.frontFace){
v1.frontFace(v163);
v5.frontFace=v163;
}
v164=v4.lineWidth;
if(v164!==v5.lineWidth){
v1.lineWidth(v164);
v5.lineWidth=v164;
}
v165=v4.polygonOffset_enable;
if(v165!==v5.polygonOffset_enable){
if(v165){
v1.enable(32823);
}
else{
v1.disable(32823);
}
v5.polygonOffset_enable=v165;
}
v166=v27[0];
v167=v27[1];
if(v166!==v28[0]||v167!==v28[1]){
v1.polygonOffset(v166,v167);
v28[0]=v166;
v28[1]=v167;
}
v168=v4.sample_alpha;
if(v168!==v5.sample_alpha){
if(v168){
v1.enable(32926);
}
else{
v1.disable(32926);
}
v5.sample_alpha=v168;
}
v169=v4.sample_enable;
if(v169!==v5.sample_enable){
if(v169){
v1.enable(32928);
}
else{
v1.disable(32928);
}
v5.sample_enable=v169;
}
v170=v29[0];
v171=v29[1];
if(v170!==v30[0]||v171!==v30[1]){
v1.sampleCoverage(v170,v171);
v30[0]=v170;
v30[1]=v171;
}
v172=v4.stencil_enable;
if(v172!==v5.stencil_enable){
if(v172){
v1.enable(2960);
}
else{
v1.disable(2960);
}
v5.stencil_enable=v172;
}
v173=v4.stencil_mask;
if(v173!==v5.stencil_mask){
v1.stencilMask(v173);
v5.stencil_mask=v173;
}
v174=v31[0];
v175=v31[1];
v176=v31[2];
if(v174!==v32[0]||v175!==v32[1]||v176!==v32[2]){
v1.stencilFunc(v174,v175,v176);
v32[0]=v174;
v32[1]=v175;
v32[2]=v176;
}
v177=v33[0];
v178=v33[1];
v179=v33[2];
v180=v33[3];
if(v177!==v34[0]||v178!==v34[1]||v179!==v34[2]||v180!==v34[3]){
v1.stencilOpSeparate(v177,v178,v179,v180);
v34[0]=v177;
v34[1]=v178;
v34[2]=v179;
v34[3]=v180;
}
v181=v35[0];
v182=v35[1];
v183=v35[2];
v184=v35[3];
if(v181!==v36[0]||v182!==v36[1]||v183!==v36[2]||v184!==v36[3]){
v1.stencilOpSeparate(v181,v182,v183,v184);
v36[0]=v181;
v36[1]=v182;
v36[2]=v183;
v36[3]=v184;
}
v185=v4.scissor_enable;
if(v185!==v5.scissor_enable){
if(v185){
v1.enable(3089);
}
else{
v1.disable(3089);
}
v5.scissor_enable=v185;
}
}
v186=v5.profile;
if(v186){
v187=performance.now();
g48.count+=a1;
}
v188=v9.frag;
v189=v9.vert;
v190=v9.program(v189,v188);
v1.useProgram(v190.program);
v11.setVAO(null);
v192=v190.id;
v193=v191[v192];
if(v193){
v193.call(this,a0,a1);
}
else{
v193=v191[v192]=g194(v190);
v193.call(this,a0,a1);
}
v5.dirty=true;
v11.setVAO(null);
if(v186){
g48.cpuTime+=performance.now()-v187;
}
}
,}

}