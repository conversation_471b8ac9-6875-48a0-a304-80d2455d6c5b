{"name": "glslify", "version": "7.1.1", "description": "A node.js-style module system for GLSL!", "main": "index.js", "bin": {"glslify": "bin.js"}, "license": "MIT", "scripts": {"test": "npm run test:node", "test:node": "node test", "test:electron": "test/electron.sh"}, "authors": ["<PERSON> <<EMAIL>> (http://hughsk.io/)", "<PERSON><PERSON><PERSON> <miko<PERSON><PERSON><PERSON>@gmail.com> (http://0fps.net)", "<PERSON> <<EMAIL>> (http://neversaw.us)"], "dependencies": {"bl": "^2.2.1", "concat-stream": "^1.5.2", "duplexify": "^3.4.5", "falafel": "^2.1.0", "from2": "^2.3.0", "glsl-resolve": "0.0.1", "glsl-token-whitespace-trim": "^1.0.0", "glslify-bundle": "^5.0.0", "glslify-deps": "^1.2.5", "minimist": "^1.2.5", "resolve": "^1.1.5", "stack-trace": "0.0.9", "static-eval": "^2.0.5", "through2": "^2.0.1", "xtend": "^4.0.0"}, "devDependencies": {"browserify": "^16.2.2", "electron-prebuilt": "^1.3.3", "electron-spawn": "^5.0.0", "glsl-easings": "^1.0.0", "glsl-noise": "0.0.0", "glslify-hex": "^2.0.1", "shell-quote": "^1.6.1", "tap-spec": "^2.2.1", "tape": "^4.6.0", "uniq": "^1.0.1"}, "repository": {"type": "git", "url": "git://github.com/stackgl/glslify.git"}, "keywords": ["ecosystem:stackgl", "browserify-transform", "glslify", "glsl", "module", "system", "cli", "shader", "webgl"], "browser": {"index.js": "./browser.js"}, "homepage": "https://github.com/stackgl/glslify", "bugs": {"url": "https://github.com/stackgl/glslify/issues"}}