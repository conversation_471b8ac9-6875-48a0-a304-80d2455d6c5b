{"name": "sequelize-cli", "version": "6.6.3", "description": "The Sequelize CLI", "types": "./types.d.ts", "bin": {"sequelize": "./lib/sequelize", "sequelize-cli": "./lib/sequelize"}, "files": ["lib", "types.d.ts"], "dependencies": {"fs-extra": "^9.1.0", "js-beautify": "1.15.4", "lodash": "^4.17.21", "picocolors": "^1.1.1", "resolve": "^1.22.1", "umzug": "^2.3.0", "yargs": "^16.2.0"}, "devDependencies": {"@babel/cli": "7.27.1", "@babel/core": "7.27.1", "@babel/preset-env": "7.27.1", "@babel/register": "7.27.1", "@commitlint/cli": "18.6.1", "@commitlint/config-angular": "18.6.1", "bluebird": "3.7.2", "eslint": "7.32.0", "eslint-config-prettier": "10.1.2", "eslint-plugin-prettier": "4.2.1", "expect.js": "0.3.1", "gulp": "4.0.2", "husky": "8.0.3", "lint-staged": "15.5.1", "mocha": "9.2.2", "mysql2": "3.2.0", "pg": "latest", "pg-hstore": "latest", "prettier": "2.8.8", "semver": "7.7.1", "sequelize": "6.37.7", "sqlite3": "latest", "through2": "4.0.2"}, "prettier": {"trailingComma": "es5", "tabWidth": 2, "semi": true, "singleQuote": true}, "scripts": {"build": "npm run build-clean && babel src -d lib && npm run build-bin && npm run build-assets", "build-bin": "mv ./lib/sequelize.js ./lib/sequelize && chmod +x ./lib/sequelize", "build-assets": "cp -R ./src/assets ./lib/", "build-clean": "rm -rf ./lib/", "lint": "eslint test src", "pretty": "prettier src test --write", "prepare": "husky install && npm run build", "test-raw": "mocha 'test/**/*.test.js'", "test": "npm run lint && npm run build && npm run test-raw"}, "repository": {"type": "git", "url": "git://github.com/sequelize/cli.git"}, "keywords": ["sequelize", "cli"], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Paulo R Lopes", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@outlook.com"}], "license": "MIT", "bugs": {"url": "https://github.com/sequelize/cli/issues"}, "homepage": "https://github.com/sequelize/cli", "engines": {"node": ">=10.0.0"}, "commitlint": {"extends": ["@commitlint/config-angular"], "rules": {"type-enum": [2, "always", ["build", "ci", "docs", "feat", "fix", "perf", "refactor", "revert", "style", "test", "meta"]]}}, "lint-staged": {"*.js": "eslint"}, "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github"], "branches": ["v6", {"name": "v7", "prerelease": "alpha"}]}, "publishConfig": {"tag": "latest"}}